"use client"

import DashboardLayout from "@/components/dashboard/dashboard-layout"
import KYCVerification from "@/components/dashboard/kyc-verification"

export default function KYCPage() {
  return (
    <DashboardLayout activeTab="kyc" onTabChange={() => {}}>
      <KYCVerification />
    </DashboardLayout>
  )
}

export const metadata = {
  title: "KYC Verification | Dashboard",
  description: "Complete your Know Your Customer verification to unlock withdrawal capabilities",
}
