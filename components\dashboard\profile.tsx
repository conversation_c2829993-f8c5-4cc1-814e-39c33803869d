"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  User,
  Mail,
  Phone,
  MapPin,
  Star,
  Edit,
  Camera,
  Shield,
  Loader2,
  AlertCircle,
  CheckCircle,
  XCircle
} from "lucide-react"
import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "sonner"

interface UserProfile {
  id: number
  username: string
  email: string
  name: string
  country: string
  phone_no: string
  address: string
  referral_code: string
  referral_code_access_enabled: boolean
  points_balance: number
  is_verified: boolean
}

export default function Profile() {
  const [isEditing, setIsEditing] = useState(false)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { isAuthenticated, getUserProfile } = useAuth()

  // Fetch user profile from API using auth context
  const fetchUserProfile = async () => {
    try {
      setIsLoading(true)
      setError(null)

      console.log('Fetching user profile using auth context...')
      const data: UserProfile = await getUserProfile()

      console.log('=== PROFILE SUCCESS ===')
      console.log('Profile data received from auth context:', {
        id: data.id,
        username: data.username,
        email: data.email,
        name: data.name,
        country: data.country,
        phone_no: data.phone_no,
        points_balance: data.points_balance,
        is_verified: data.is_verified
      })

      setUserProfile(data)
      toast.success("Profile loaded successfully")

    } catch (error) {
      console.error('=== PROFILE ERROR ===')
      console.error('Error details:', error)
      setError(error instanceof Error ? error.message : "Failed to load profile data")
      toast.error("Failed to load profile data")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (isAuthenticated) {
      fetchUserProfile()
    }
  }, [isAuthenticated])

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600 dark:text-gray-300">Loading profile...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Error Loading Profile</h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">{error}</p>
              <Button onClick={fetchUserProfile} className="bg-blue-600 hover:bg-blue-700 text-white">
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // No profile data
  if (!userProfile) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No Profile Data</h3>
              <p className="text-gray-600 dark:text-gray-300">Unable to load profile information.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const getVerificationStatus = () => {
    if (userProfile?.is_verified) {
      return {
        icon: CheckCircle,
        text: "Verified",
        color: "text-green-600",
        bgColor: "bg-green-100 dark:bg-green-900/30"
      }
    } else {
      return {
        icon: XCircle,
        text: "Unverified",
        color: "text-red-600",
        bgColor: "bg-red-100 dark:bg-red-900/30"
      }
    }
  }

  const verificationStatus = getVerificationStatus()

  return (
    <div className="space-y-8">
      {/* Profile Header */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800">
        <CardContent className="pt-6">
          <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6">
            <div className="relative">
              <div className="w-20 h-20 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold text-xl shadow-lg">
                {userProfile.name.split(' ').map(n => n[0]).join('').toUpperCase()}
              </div>
              <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full flex items-center justify-center ${verificationStatus.bgColor}`}>
                <verificationStatus.icon className={`w-4 h-4 ${verificationStatus.color}`} />
              </div>
            </div>

            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{userProfile.name}</h1>
                <Badge className={`${verificationStatus.bgColor} ${verificationStatus.color} border-0`}>
                  {verificationStatus.text}
                </Badge>
              </div>
              <p className="text-gray-600 dark:text-gray-300 mb-1">@{userProfile.username}</p>
              <p className="text-gray-500 dark:text-gray-400 text-sm">User ID: {userProfile.id}</p>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setIsEditing(!isEditing)}
                className="border-blue-200 dark:border-blue-800"
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit Profile
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5 text-blue-600" />
              Personal Information
            </CardTitle>
            <CardDescription>Your account details and contact information</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <User className="w-4 h-4 inline mr-2" />
                  Full Name
                </label>
                <Input
                  value={userProfile.name}
                  disabled={!isEditing}
                  className="bg-gray-50 dark:bg-gray-800"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <User className="w-4 h-4 inline mr-2" />
                  Username
                </label>
                <Input
                  value={userProfile.username}
                  disabled={!isEditing}
                  className="bg-gray-50 dark:bg-gray-800"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <Mail className="w-4 h-4 inline mr-2" />
                  Email Address
                </label>
                <Input
                  value={userProfile.email}
                  disabled={!isEditing}
                  className="bg-gray-50 dark:bg-gray-800"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <Phone className="w-4 h-4 inline mr-2" />
                  Phone Number
                </label>
                <Input
                  value={userProfile.phone_no}
                  disabled={!isEditing}
                  className="bg-gray-50 dark:bg-gray-800"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <MapPin className="w-4 h-4 inline mr-2" />
                  Country
                </label>
                <Input
                  value={userProfile.country}
                  disabled={!isEditing}
                  className="bg-gray-50 dark:bg-gray-800"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <MapPin className="w-4 h-4 inline mr-2" />
                  Address
                </label>
                <Input
                  value={userProfile.address}
                  disabled={!isEditing}
                  className="bg-gray-50 dark:bg-gray-800"
                />
              </div>
            </div>

            {isEditing && (
              <div className="flex gap-2 mt-6">
                <Button className="bg-green-600 hover:bg-green-700 text-white">
                  Save Changes
                </Button>
                <Button variant="outline" onClick={() => setIsEditing(false)}>
                  Cancel
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Account Statistics & Referral */}
        <div className="space-y-6">
          {/* Points Balance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-500" />
                Points Balance
              </CardTitle>
              <CardDescription>Your current points balance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-6">
                <div className="text-4xl font-bold text-yellow-600 mb-2">
                  {userProfile.points_balance.toLocaleString()}
                </div>
                <p className="text-gray-600 dark:text-gray-300">Total Points</p>
              </div>
            </CardContent>
          </Card>

          {/* Referral Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5 text-blue-500" />
                Referral Program
              </CardTitle>
              <CardDescription>Share your referral code and earn rewards</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Your Referral Code
                </label>
                <div className="flex gap-2">
                  <Input
                    value={userProfile.referral_code}
                    readOnly
                    className="bg-gray-50 dark:bg-gray-800 font-mono"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(userProfile.referral_code)
                      toast.success("Referral code copied to clipboard!")
                    }}
                  >
                    Copy
                  </Button>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${userProfile.referral_code_access_enabled ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-sm text-gray-600 dark:text-gray-300">
                  Referral program is {userProfile.referral_code_access_enabled ? 'active' : 'inactive'}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Account Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-purple-500" />
                Account Status
              </CardTitle>
              <CardDescription>Your account verification and status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                  <span className="font-medium text-gray-900 dark:text-white">Verification Status</span>
                  <Badge className={`${verificationStatus.bgColor} ${verificationStatus.color} border-0`}>
                    <verificationStatus.icon className="w-3 h-3 mr-1" />
                    {verificationStatus.text}
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                  <span className="font-medium text-gray-900 dark:text-white">Account ID</span>
                  <span className="text-gray-600 dark:text-gray-300 font-mono">#{userProfile.id}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
