"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import {
  Shield,
  FileText,
  CheckCircle,
  Clock,
  AlertCircle,
  User,
  CreditCard,
  Camera,
  TrendingUp,
  Brain,
  Target,
  Award,
  ArrowRight,
  Upload,
  Eye,
  Lock,
} from "lucide-react"
import { useState } from "react"
import { useKYC } from "@/contexts/kyc-context"

export default function KYCVerification() {
  const { kycData, updateCurrentStep, updateKYCStatus, submitKYC } = useKYC()
  const currentStep = kycData.currentStep
  const kycStatus = kycData.status

  const totalSteps = 4
  const progressPercentage = (currentStep / totalSteps) * 100

  const evaluationChallenges = [
    {
      id: 1,
      title: "Risk Management Assessment",
      description: "Demonstrate your understanding of risk management principles and position sizing",
      icon: Shield,
      status: "not_started", // 'not_started', 'in_progress', 'completed', 'failed'
      difficulty: "Intermediate",
      estimatedTime: "15 minutes",
      requirements: [
        "Calculate position sizes based on account balance",
        "Identify appropriate stop-loss levels",
        "Demonstrate risk-reward ratio understanding"
      ]
    },
    {
      id: 2,
      title: "Market Analysis Challenge",
      description: "Analyze market conditions and identify trading opportunities",
      icon: TrendingUp,
      status: "locked",
      difficulty: "Advanced",
      estimatedTime: "20 minutes",
      requirements: [
        "Technical analysis of provided charts",
        "Fundamental analysis interpretation",
        "Market sentiment evaluation"
      ]
    },
    {
      id: 3,
      title: "Trading Psychology Evaluation",
      description: "Assess your emotional control and decision-making under pressure",
      icon: Brain,
      status: "locked",
      difficulty: "Intermediate",
      estimatedTime: "10 minutes",
      requirements: [
        "Scenario-based decision making",
        "Emotional response assessment",
        "Discipline and patience evaluation"
      ]
    },
    {
      id: 4,
      title: "Compliance & Ethics Test",
      description: "Verify understanding of trading regulations and ethical practices",
      icon: Award,
      status: "locked",
      difficulty: "Beginner",
      estimatedTime: "12 minutes",
      requirements: [
        "Regulatory compliance knowledge",
        "Ethical trading practices",
        "Platform rules understanding"
      ]
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
      case "in_progress":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
      case "locked":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
      default:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Beginner":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
      case "Intermediate":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
      case "Advanced":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">KYC Verification</h1>
        <p className="text-gray-600 dark:text-gray-300">
          Complete your Know Your Customer verification to unlock withdrawal capabilities
        </p>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            Verification Progress
          </CardTitle>
          <CardDescription>Complete all steps to verify your account</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Step {currentStep} of {totalSteps}
              </span>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {Math.round(progressPercentage)}% Complete
              </span>
            </div>
            <Progress value={progressPercentage} className="w-full" />
          </div>
        </CardContent>
      </Card>

      {/* Current Status */}
      <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
        <CardContent className="pt-6">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
              <Clock className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Verification Required</h3>
              <p className="text-gray-700 dark:text-gray-300 mb-4">
                To comply with financial regulations and ensure account security, you must complete our comprehensive 
                KYC verification process. This includes passing evaluation challenges that test your trading knowledge 
                and risk management skills.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                  <Eye className="w-3 h-3 mr-1" />
                  Identity Verification
                </Badge>
                <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                  <Brain className="w-3 h-3 mr-1" />
                  Trading Evaluation
                </Badge>
                <Badge className="bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400">
                  <Lock className="w-3 h-3 mr-1" />
                  Compliance Check
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 1: Personal Information */}
      {currentStep === 1 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              Step 1: Personal Information
            </CardTitle>
            <CardDescription>Provide your personal details for identity verification</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input id="firstName" placeholder="Enter your first name" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input id="lastName" placeholder="Enter your last name" />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dateOfBirth">Date of Birth</Label>
                <Input id="dateOfBirth" type="date" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="nationality">Nationality</Label>
                <Input id="nationality" placeholder="Enter your nationality" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Full Address</Label>
              <Textarea id="address" placeholder="Enter your complete address" />
            </div>

            <Button
              onClick={() => updateCurrentStep(2)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              Continue to Document Upload
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Step 2: Document Upload */}
      {currentStep === 2 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              Step 2: Document Verification
            </CardTitle>
            <CardDescription>Upload required documents for identity verification</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900 dark:text-white">Government ID</h4>
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                  <Camera className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                    Upload a clear photo of your government-issued ID
                  </p>
                  <Button variant="outline" size="sm">
                    <Upload className="w-4 h-4 mr-2" />
                    Choose File
                  </Button>
                </div>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900 dark:text-white">Proof of Address</h4>
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                  <FileText className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                    Upload a utility bill or bank statement (max 3 months old)
                  </p>
                  <Button variant="outline" size="sm">
                    <Upload className="w-4 h-4 mr-2" />
                    Choose File
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex gap-4">
              <Button
                variant="outline"
                onClick={() => updateCurrentStep(1)}
                className="flex-1"
              >
                Back
              </Button>
              <Button
                onClick={() => updateCurrentStep(3)}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
              >
                Continue to Evaluation
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 3: Evaluation Challenges */}
      {currentStep === 3 && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                Step 3: Trading Evaluation Challenges
              </CardTitle>
              <CardDescription>
                Complete these challenges to demonstrate your trading knowledge and skills
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
                <div className="flex items-start gap-3">
                  <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-1">Important Requirements</h4>
                    <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                      <li>• You must pass all challenges with a minimum score of 80%</li>
                      <li>• Challenges must be completed in order</li>
                      <li>• You have 3 attempts per challenge</li>
                      <li>• Each challenge has a time limit</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="grid gap-6">
                {evaluationChallenges.map((challenge, index) => {
                  const IconComponent = challenge.icon
                  const isLocked = challenge.status === 'locked'
                  const isCompleted = challenge.status === 'completed'

                  return (
                    <Card
                      key={challenge.id}
                      className={`transition-all duration-200 ${
                        isLocked ? 'opacity-60' : 'hover:shadow-lg'
                      } ${isCompleted ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' : ''}`}
                    >
                      <CardContent className="pt-6">
                        <div className="flex items-start gap-4">
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                            isCompleted
                              ? 'bg-green-100 dark:bg-green-900/30'
                              : isLocked
                                ? 'bg-gray-100 dark:bg-gray-800'
                                : 'bg-blue-100 dark:bg-blue-900/30'
                          }`}>
                            {isCompleted ? (
                              <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                            ) : (
                              <IconComponent className={`w-6 h-6 ${
                                isLocked
                                  ? 'text-gray-400'
                                  : 'text-blue-600 dark:text-blue-400'
                              }`} />
                            )}
                          </div>

                          <div className="flex-1">
                            <div className="flex items-start justify-between mb-2">
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {challenge.title}
                              </h3>
                              <div className="flex gap-2">
                                <Badge className={getStatusColor(challenge.status)}>
                                  {challenge.status.replace('_', ' ')}
                                </Badge>
                                <Badge className={getDifficultyColor(challenge.difficulty)}>
                                  {challenge.difficulty}
                                </Badge>
                              </div>
                            </div>

                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                              {challenge.description}
                            </p>

                            <div className="flex items-center gap-4 mb-4 text-sm text-gray-500 dark:text-gray-400">
                              <span className="flex items-center gap-1">
                                <Clock className="w-4 h-4" />
                                {challenge.estimatedTime}
                              </span>
                            </div>

                            <div className="mb-4">
                              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Requirements:</h4>
                              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                                {challenge.requirements.map((req, reqIndex) => (
                                  <li key={reqIndex} className="flex items-start gap-2">
                                    <span className="text-blue-600 dark:text-blue-400 mt-1">•</span>
                                    {req}
                                  </li>
                                ))}
                              </ul>
                            </div>

                            <Button
                              disabled={isLocked || isCompleted}
                              className={`${
                                isCompleted
                                  ? 'bg-green-600 hover:bg-green-700'
                                  : 'bg-blue-600 hover:bg-blue-700'
                              } text-white`}
                            >
                              {isCompleted ? (
                                <>
                                  <CheckCircle className="w-4 h-4 mr-2" />
                                  Completed
                                </>
                              ) : isLocked ? (
                                <>
                                  <Lock className="w-4 h-4 mr-2" />
                                  Locked
                                </>
                              ) : (
                                <>
                                  <Target className="w-4 h-4 mr-2" />
                                  Start Challenge
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>

              <div className="flex gap-4 mt-6">
                <Button
                  variant="outline"
                  onClick={() => updateCurrentStep(2)}
                  className="flex-1"
                >
                  Back
                </Button>
                <Button
                  onClick={() => updateCurrentStep(4)}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Continue to Final Review
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Step 4: Final Review */}
      {currentStep === 4 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
              Step 4: Final Review & Submission
            </CardTitle>
            <CardDescription>Review your information and submit for verification</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Verification Complete</h4>
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    Congratulations! You have successfully completed all KYC requirements. Your submission
                    will be reviewed by our compliance team within 24-48 hours.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-white mb-1">Completed Steps:</h5>
                      <ul className="text-gray-600 dark:text-gray-300 space-y-1">
                        <li>✓ Personal Information</li>
                        <li>✓ Document Upload</li>
                        <li>✓ Trading Evaluation</li>
                      </ul>
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-white mb-1">Next Steps:</h5>
                      <ul className="text-gray-600 dark:text-gray-300 space-y-1">
                        <li>• Review by compliance team</li>
                        <li>• Email notification of status</li>
                        <li>• Withdrawal access upon approval</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex gap-4">
              <Button
                variant="outline"
                onClick={() => updateCurrentStep(3)}
                className="flex-1"
              >
                Back
              </Button>
              <Button
                onClick={() => {
                  submitKYC()
                  updateKYCStatus('in_progress')
                }}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white"
              >
                Submit for Review
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
